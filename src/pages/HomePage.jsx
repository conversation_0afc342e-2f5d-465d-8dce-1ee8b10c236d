import React, { memo, useMemo } from 'react'
import CourseCard from '../components/CourseCard'
import coursesData from '../data/courses.json'

const HomePage = memo(() => {
  const courses = useMemo(() => coursesData.courses, [])

  return (
    <div className="min-h-screen">
      {/* 头部区域 */}
      <header className="fade-in">
        <div className="mx-auto px-8 lg:px-16 xl:px-20 2xl:px-32 py-16 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-slate-800 mb-6">
            英语课程学习平台
          </h1>
          <p className="text-slate-600 text-xl max-w-4xl mx-auto leading-relaxed">
            系统性掌握英语语法、词汇和表达技巧，从基础到高级的完整学习路径
          </p>
        </div>
      </header>

      {/* 课程展示区域 */}
      <main className="mx-auto px-8 lg:px-16 xl:px-20 2xl:px-32">
        <div className="fade-in" style={{ animationDelay: '100ms' }}>
          {/* 响应式网格布局 - 适中显示 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-4 gap-6">
            {courses.map((course, index) => (
              <CourseCard
                key={course.id}
                course={course}
                index={index}
              />
            ))}
          </div>
        </div>
      </main>

      {/* 底部信息 */}
      <footer className="mt-24 pb-16 fade-in" style={{ animationDelay: '200ms' }}>
        <div className="mx-auto px-8 lg:px-16 xl:px-20 2xl:px-32 text-center">
          <div className="text-slate-500 text-sm">
            <p>© 2024 英语课程学习平台</p>
          </div>
        </div>
      </footer>
    </div>
  )
})

HomePage.displayName = 'HomePage'

export default HomePage
