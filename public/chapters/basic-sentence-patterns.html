<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基本句型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }

        .card {
            transition: transform 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-white">
<div class="p-8 max-w-7xl mx-auto">
    <!-- 1. 陈述句（Declarative Sentence） -->
    <section class="mb-16">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">1. 陈述句（Declarative Sentence）</h2>
        <p class="text-gray-600 mb-8 text-lg">陈述句是用来陈述事实、表达观点或描述情况的句子。它是最常用的句型，通常以句号结尾。陈述句的基本语序是：主语
            + 谓语 + 宾语/表语。</p>

        <!-- 1.1 基本结构 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">1.1 基本结构</h3>

            <!-- 主谓结构 -->
            <div class="mb-8">
                <h4 class="text-xl font-semibold text-gray-700 mb-4">主谓结构（Subject + Verb）</h4>
                <p class="text-gray-600 mb-4">最简单的陈述句结构，只包含主语和谓语动词。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">基础句型</div>
                        <div class="keyword text-lg mb-1">Birds fly.</div>
                        <div class="text-sm text-gray-600 mb-1">/bərdz flaɪ/</div>
                        <div class="text-gray-700">鸟儿飞翔。</div>
                    </div>

                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">人称主语</div>
                        <div class="keyword text-lg mb-1">She sings.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi sɪŋz/</div>
                        <div class="text-gray-700">她唱歌。</div>
                    </div>

                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">复数主语</div>
                        <div class="keyword text-lg mb-1">Children play.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈtʃɪldrən pleɪ/</div>
                        <div class="text-gray-700">孩子们玩耍。</div>
                    </div>
                </div>
            </div>

            <!-- 主谓宾结构 -->
            <div class="mb-8">
                <h4 class="text-xl font-semibold text-gray-700 mb-4">主谓宾结构（Subject + Verb + Object）</h4>
                <p class="text-gray-600 mb-4">包含主语、及物动词和宾语的完整结构。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">动作对象</div>
                        <div class="keyword text-lg mb-1">I read books.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ rid bʊks/</div>
                        <div class="text-gray-700">我读书。</div>
                    </div>

                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">具体宾语</div>
                        <div class="keyword text-lg mb-1">She drinks coffee.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi drɪŋks ˈkɔfi/</div>
                        <div class="text-gray-700">她喝咖啡。</div>
                    </div>

                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">人物宾语</div>
                        <div class="keyword text-lg mb-1">We visit our friends.</div>
                        <div class="text-sm text-gray-600 mb-1">/wi ˈvɪzɪt aʊr frɛndz/</div>
                        <div class="text-gray-700">我们拜访朋友。</div>
                    </div>

                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">抽象宾语</div>
                        <div class="keyword text-lg mb-1">He loves music.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi lʌvz ˈmjuzɪk/</div>
                        <div class="text-gray-700">他喜欢音乐。</div>
                    </div>
                </div>
            </div>

            <!-- 主系表结构 -->
            <div class="mb-8">
                <h4 class="text-xl font-semibold text-gray-700 mb-4">主系表结构（Subject + Linking Verb +
                    Complement）</h4>
                <p class="text-gray-600 mb-4">使用系动词连接主语和表语，描述主语的状态或特征。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">be动词 + 形容词</div>
                        <div class="keyword text-lg mb-1">The weather is beautiful.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈwɛðər ɪz ˈbjutəfəl/</div>
                        <div class="text-gray-700">天气很美丽。</div>
                    </div>

                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">be动词 + 名词</div>
                        <div class="keyword text-lg mb-1">She is a doctor.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ə ˈdɑktər/</div>
                        <div class="text-gray-700">她是一名医生。</div>
                    </div>

                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">感官动词</div>
                        <div class="keyword text-lg mb-1">The food tastes delicious.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə fud teɪsts dɪˈlɪʃəs/</div>
                        <div class="text-gray-700">食物很美味。</div>
                    </div>

                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">变化动词</div>
                        <div class="keyword text-lg mb-1">He becomes tired.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi bɪˈkʌmz ˈtaɪərd/</div>
                        <div class="text-gray-700">他变得疲倦。</div>
                    </div>
                </div>
            </div>

            <!-- 主谓双宾结构 -->
            <div class="mb-8">
                <h4 class="text-xl font-semibold text-gray-700 mb-4">主谓双宾结构（Subject + Verb + Indirect Object +
                    Direct Object）</h4>
                <p class="text-gray-600 mb-4">包含间接宾语和直接宾语的结构，通常表示给予或传递的动作。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">给予动作</div>
                        <div class="keyword text-lg mb-1">I gave him a book.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ ɡeɪv hɪm ə bʊk/</div>
                        <div class="text-gray-700">我给了他一本书。</div>
                    </div>

                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">展示动作</div>
                        <div class="keyword text-lg mb-1">She showed me her photos.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi ʃoʊd mi hər ˈfoʊtoʊz/</div>
                        <div class="text-gray-700">她给我看了她的照片。</div>
                    </div>

                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">传递信息</div>
                        <div class="keyword text-lg mb-1">He told us the story.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi toʊld ʌs ðə ˈstɔri/</div>
                        <div class="text-gray-700">他给我们讲了这个故事。</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 1.2 时态变化 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">1.2 时态变化</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一般现在时</div>
                    <div class="keyword text-lg mb-1">She works every day.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi wərks ˈɛvri deɪ/</div>
                    <div class="text-gray-700">她每天工作。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一般过去时</div>
                    <div class="keyword text-lg mb-1">I visited Paris last year.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ˈvɪzɪtəd ˈpɛrɪs læst jɪr/</div>
                    <div class="text-gray-700">我去年访问了巴黎。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一般将来时</div>
                    <div class="keyword text-lg mb-1">We will meet tomorrow.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi wɪl mit təˈmɑroʊ/</div>
                    <div class="text-gray-700">我们明天会见面。</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 2. 疑问句（Interrogative Sentence） -->
    <section class="mb-16">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">2. 疑问句（Interrogative Sentence）</h2>
        <p class="text-gray-600 mb-8 text-lg">
            疑问句用来提出问题，获取信息。疑问句通常以问号结尾，语调上扬。根据结构和功能，疑问句分为四种基本类型。</p>

        <!-- 2.1 一般疑问句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">2.1 一般疑问句（Yes/No Questions）</h3>
            <p class="text-gray-600 mb-4">用来询问是否的问题，答案通常是yes或no。结构是：助动词/be动词/情态动词 + 主语 +
                其他成分？</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">be动词提前</div>
                    <div class="keyword text-lg mb-1">Are you a student?</div>
                    <div class="text-sm text-gray-600 mb-1">/ɑr ju ə ˈstudənt/</div>
                    <div class="text-gray-700">你是学生吗？</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">助动词do</div>
                    <div class="keyword text-lg mb-1">Do you like pizza?</div>
                    <div class="text-sm text-gray-600 mb-1">/du ju laɪk ˈpitsə/</div>
                    <div class="text-gray-700">你喜欢披萨吗？</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">助动词does</div>
                    <div class="keyword text-lg mb-1">Does she speak English?</div>
                    <div class="text-sm text-gray-600 mb-1">/dʌz ʃi spik ˈɪŋɡlɪʃ/</div>
                    <div class="text-gray-700">她说英语吗？</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情态动词</div>
                    <div class="keyword text-lg mb-1">Can you help me?</div>
                    <div class="text-sm text-gray-600 mb-1">/kæn ju hɛlp mi/</div>
                    <div class="text-gray-700">你能帮助我吗？</div>
                </div>
            </div>
        </div>

        <!-- 2.2 特殊疑问句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">2.2 特殊疑问句（Wh-Questions）</h3>
            <p class="text-gray-600 mb-4">以疑问词开头的问句，用来询问具体信息。结构是：疑问词 + 助动词/be动词 + 主语 +
                其他成分？</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">what询问事物</div>
                    <div class="keyword text-lg mb-1">What is your name?</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ɪz jʊr neɪm/</div>
                    <div class="text-gray-700">你的名字是什么？</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">where询问地点</div>
                    <div class="keyword text-lg mb-1">Where do you live?</div>
                    <div class="text-sm text-gray-600 mb-1">/wɛr du ju lɪv/</div>
                    <div class="text-gray-700">你住在哪里？</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">when询问时间</div>
                    <div class="keyword text-lg mb-1">When will you come?</div>
                    <div class="text-sm text-gray-600 mb-1">/wɛn wɪl ju kʌm/</div>
                    <div class="text-gray-700">你什么时候来？</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">who询问人物</div>
                    <div class="keyword text-lg mb-1">Who is that man?</div>
                    <div class="text-sm text-gray-600 mb-1">/hu ɪz ðæt mæn/</div>
                    <div class="text-gray-700">那个人是谁？</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">why询问原因</div>
                    <div class="keyword text-lg mb-1">Why are you late?</div>
                    <div class="text-sm text-gray-600 mb-1">/waɪ ɑr ju leɪt/</div>
                    <div class="text-gray-700">你为什么迟到？</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">how询问方式</div>
                    <div class="keyword text-lg mb-1">How do you go to work?</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ du ju ɡoʊ tu wərk/</div>
                    <div class="text-gray-700">你怎么去上班？</div>
                </div>
            </div>
        </div>

        <!-- 2.3 选择疑问句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">2.3 选择疑问句（Alternative Questions）</h3>
            <p class="text-gray-600 mb-4">提供两个或多个选项供选择，用or连接选项。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">两个选项</div>
                    <div class="keyword text-lg mb-1">Do you want tea or coffee?</div>
                    <div class="text-sm text-gray-600 mb-1">/du ju wʌnt ti ɔr ˈkɔfi/</div>
                    <div class="text-gray-700">你想要茶还是咖啡？</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">多个选项</div>
                    <div class="keyword text-lg mb-1">Which color do you prefer, red, blue, or green?</div>
                    <div class="text-sm text-gray-600 mb-1">/wɪtʃ ˈkʌlər du ju prɪˈfər rɛd blu ɔr ɡrin/</div>
                    <div class="text-gray-700">你更喜欢哪种颜色，红色、蓝色还是绿色？</div>
                </div>
            </div>
        </div>

        <!-- 2.4 反意疑问句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">2.4 反意疑问句（Tag Questions）</h3>
            <p class="text-gray-600 mb-4">
                在陈述句后面加上简短的疑问部分，用来确认信息或寻求同意。遵循"前肯后否，前否后肯"的原则。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">前肯后否</div>
                    <div class="keyword text-lg mb-1">You are a teacher, aren't you?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju ɑr ə ˈtitʃər ˌɑrənt ju/</div>
                    <div class="text-gray-700">你是老师，对吗？</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">前否后肯</div>
                    <div class="keyword text-lg mb-1">She doesn't like music, does she?</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈdʌzənt laɪk ˈmjuzɪk dʌz ʃi/</div>
                    <div class="text-gray-700">她不喜欢音乐，对吗？</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情态动词</div>
                    <div class="keyword text-lg mb-1">You can swim, can't you?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju kæn swɪm kænt ju/</div>
                    <div class="text-gray-700">你会游泳，对吗？</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 3. 祈使句（Imperative Sentence） -->
    <section class="mb-16">
        <h2 class="text-3xl font-bold text-gray-800 mb-8">3. 祈使句（Imperative Sentence）</h2>
        <p class="text-gray-600 mb-8 text-lg">
            祈使句用来表达请求、命令、建议、邀请等。通常省略主语you，以动词原形开头，语调比较直接。</p>

        <!-- 3.1 肯定祈使句 -->
        <div class="mb-10">
            <h3 class="text-2xl font-bold text-gray-700 mb-6">3.1 肯定祈使句</h3>
            <p class="text-gray-600 mb-4">直接以动词原形开头，表示要求对方做某事。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">简单命令</div>
                    <div class="keyword text-lg mb-1">Sit down.</div>
                    <div class="text-sm text-gray-600 mb-1">/sɪt daʊn/</div>
                    <div class="text-gray-700">坐下。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">礼貌请求</div>
                    <div class="keyword text-lg mb-1">Please help me.</div>
                    <div class="text-sm text-gray-600 mb-1">/pliz hɛlp mi/</div>
                    <div class="text-gray-700">请帮助我。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">具体指示</div>
                    <div class="keyword text-lg mb-1">Open the window.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈoʊpən ðə ˈwɪndoʊ/</div>
                    <div class="text-gray-700">打开窗户。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">动作建议</div>
                    <div class="keyword text-lg mb-1">Take a break.</div>
                    <div class="text-sm text-gray-600 mb-1">/teɪk ə breɪk/</div>
                    <div class="text-gray-700">休息一下。</div>
                </div>
            </div>
        </div>
    </section>
</div>